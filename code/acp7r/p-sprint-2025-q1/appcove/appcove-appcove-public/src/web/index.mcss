            /* Import Google Fonts for exact typography matching */
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

            div{
                max-width: 1600px;
                margin: 0 auto;
                padding: 5px;
            }

            /* Page Container with Curved Background */
            .page-container {
                position: relative;
                overflow: hidden;
                min-height: 100vh;
                background-color: #f0ebe5; /* Light beige background exactly like in reference */
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            /* Large Curved Background Section - Circular shape from left */
            .curved-bg-primary {
                position: absolute;
                top: -250px;
                left: -350px;
                width: 700px;
                height: 700px;
                background-color: #2c5f7a; /* Dark blue matching the reference */
                border-radius: 50%;
                z-index: 1;
            }

            /* Company Profile Image positioned within curved section */
            .top-profile-image {
                position: absolute;
                top: 140px;
                left: 140px;
                z-index: 10;
                width: 200px;
                height: 200px;
                border-radius: 50%;
                overflow: hidden;
                border: none;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
            about-appcove {
                position: relative;
                z-index: 2;
                padding-top: 80px;
                padding-left: 420px; /* Move content to the right of curved section */
                padding-right: 80px;
                max-width: 1200px;

                #page-heading {
                    .hero-content {
                        margin-left: 0;
                        padding-left: 0;
                    }

                    h1 {
                        font-family: 'Inter', sans-serif;
                        font-size: 64px;
                        font-weight: 300;
                        color: #2c5f7a; /* Dark blue matching reference */
                        margin-bottom: 24px;
                        line-height: 1.1;
                        letter-spacing: -0.02em;
                    }

                    p {
                        font-family: 'Inter', sans-serif;
                        font-size: 28px;
                        font-weight: 600;
                        color: #00bfa5; /* Teal color for main tagline */
                        margin-bottom: 32px;
                        line-height: 1.3;
                        letter-spacing: -0.01em;
                    }

                    strong {
                        color: #2c5f7a; /* Dark blue for emphasis */
                        font-weight: 700;
                    }
                }
                #page-body {
                    margin-top: 24px;

                    header {
                        p {
                            font-family: 'Inter', sans-serif;
                            font-size: 20px;
                            font-weight: 600;
                            color: #2c5f7a;
                            margin-bottom: 24px;
                            line-height: 1.5;
                            letter-spacing: -0.01em;
                        }
                    }

                    p {
                        font-family: 'Inter', sans-serif;
                        font-size: 16px;
                        font-weight: 400;
                        color: #4a5568;
                        margin-bottom: 16px;
                        line-height: 1.6;
                    }

                    content {
                        font-family: 'Inter', sans-serif;
                        font-size: 16px;
                        font-weight: 400;
                        color: #4a5568;
                        line-height: 1.6;
                        display: block;
                        margin-bottom: 32px;
                    }

                    strong {
                        color: #2c5f7a;
                        font-weight: 700;
                    }

                    br {
                        margin-bottom: 8px;
                    }
                }

                /* Company Information Section */
                p {
                    font-family: 'Inter', sans-serif;
                    font-size: 14px;
                    font-weight: 600;
                    color: #2c5f7a;
                    line-height: 1.8;
                    margin-bottom: 4px;
                    letter-spacing: 0.02em;

                    strong {
                        color: #00bfa5; /* Teal for labels */
                        font-weight: 700;
                        margin-right: 8px;
                        text-transform: uppercase;
                        font-size: 12px;
                        letter-spacing: 0.1em;
                    }
                }

                #page-footer {
                    margin-top: 48px;

                    p {
                        margin-bottom: 12px;
                        font-family: 'Inter', sans-serif;
                        font-size: 14px;
                        font-weight: 600;
                        color: #2c5f7a;
                        line-height: 1.8;
                    }

                    strong {
                        color: #00bfa5;
                        font-weight: 700;
                        text-transform: uppercase;
                        font-size: 12px;
                        letter-spacing: 0.1em;
                        margin-right: 8px;
                    }

                    br {
                        margin-bottom: 4px;
                    }
                }

                #page-main {
                    margin-top: 2rem;
                    display: flex;
                    flex-direction: column;
                    gap: 2rem;

                    header-2 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    p {
                        font-size: 1rem;
                        color: #4A4A4A;
                        line-height: 1.6;
                    }
                    br {
                        margin-bottom: 0.5rem;
                    }
                    header-3 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    hr {
                        all: initial;
                        display: block;
                        border-bottom: 3.2px dotted rgb(9, 81, 129);
                    }   
                    .arrow-icon-wrapper {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 3rem;
                        height: 3rem;
                        border-radius: 80%;
                        background-color: #007b8a;
                        margin-top: 2rem;
                        margin-bottom: 3rem;
                        cursor: pointer;
                        transition: all 0.3s ease;

                    &:hover {
                        background-color: #0887a7;
                    }
                    .arrow-icon {
                        color: white;
                        font-size: 2rem;
                        transition: all 0.3s ease;
                    }
                }
            }

                #page-body {
                    margin-bottom: 1rem;
                    margin-top: 1rem;

                    h6 {
                        font-size: 2rem;
                        color: #009688;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                }
                .staff-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 350px);
                    gap: 20px 25px; /* row-gap column-gap */

                }
                .staff-card {
                    background-color: #F5F0E9;
                    border-radius: 12px;
                    width: 350px;
                    padding: 20px;
                    margin-bottom: 0;
                    height: 120px;
                    flex-direction: inherit;
                    justify-content: center;
                h4 {
                        font-size: 1.3rem;
                        color: #000000;
                        font-weight: bold;
                        font-style: open sans;
                }
                p   {
                        font-size: 1rem;
                        color: #333333;
                        
                    }
                years {
                        font-size: 1rem;
                        color:  #009688;
                        font-weight: bold;
                        font-style: open sans;
                    }
                }

                #page-footer {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    strong {
                        font-weight: bold;
                    }
                }

                #page-body {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    strong  {
                        font-weight: bold;
                    }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                }

                #timeline-panel {
                        border-radius: 0.5rem;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        width: 500px;
                        background-color: bisque;
                    
                    h5 {
                        font-weight: bold;
                        color: #21536C;
                        font-size: 2rem;
                        line-height: 1;
                    }
                    p.notable{
                        font-size: 1rem;
                        color: #05475e;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    .timeline-container {
                        position: relative;
                        margin-left: 10px;
                        border-left: 2px dotted #129686;
                        margin-bottom: 1rem;
                        max-width: 1000px;
                    }
                    .timeline-item {
                        position: relative;
                        padding-left: 1rem;
                        margin-bottom: 2rem;
                    }
                    .timeline-dot {
                        height: 16px;
                        width: 16px;
                        background-color: #119191;
                        border-radius: 50%;
                        display: inline-block;
                        margin-right: 8px;
                        position: absolute;
                        left: -7px;
                        top: 3px;
                    }
                    .timeline-year {
                        font-weight: bold;
                        color: #054f5a;
                    }
                    .active-project {
                        font-weight: 600;
                        color: #119191;
                        font-style: italic;
                    }
                    .inactive-project {
                        font-weight: 600;
                        color: #292c2e;
                        font-style: italic;
                    }
                    strong {
                        color: #01070ff8;
                        font-weight: bold;
                        font-size: 1.3rem;
                    }
                    small {
                        color:#4A4A4A ;
                        font-weight: bold;
                        font-size: 1.2rem;
                    }
                }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                    .company-logo {
                        justify-content: left;
                        align-items: left;
                        margin-top: 2rem;
                        
                    img {
                        height: 50px;
                        width: auto;
                    }
                }

                /* Responsive Design for Mobile */
                @media (max-width: 768px) {
                    .page-container {
                        background-color: #f0ebe5;
                    }

                    .curved-bg-primary {
                        top: -200px;
                        left: -250px;
                        width: 500px;
                        height: 500px;
                    }

                    .top-profile-image {
                        width: 150px;
                        height: 150px;
                        top: 100px;
                        left: 100px;
                    }

                    about-appcove {
                        padding-top: 280px;
                        padding-left: 20px;
                        padding-right: 20px;
                        max-width: 100%;

                        #page-heading h1 {
                            font-size: 42px;
                            line-height: 1.2;
                        }

                        #page-heading p {
                            font-size: 22px;
                            line-height: 1.4;
                        }
                    }

                    #timeline-panel {
                        width: 100%;
                        max-width: 400px;
                    }
                }
            }






